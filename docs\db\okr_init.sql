-- OKR系统数据库初始化脚本
-- 数据库名：OKR

-- 1. 员工表
CREATE TABLE IF NOT EXISTS `user` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '员工ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `realname` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `password` VARCHAR(100) NOT NULL COMMENT '密码',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `department_id` INT DEFAULT NULL COMMENT '部门ID',
  `position_id` INT DEFAULT NULL COMMENT '岗位ID',
  `status` TINYINT DEFAULT 1 COMMENT '状态 1在职 0离职',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工表';

-- 2. 部门表
CREATE TABLE IF NOT EXISTS `department` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
  `name` VARCHAR(50) NOT NULL COMMENT '部门名称',
  `parent_id` INT DEFAULT NULL COMMENT '上级部门ID',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 3. 岗位表
CREATE TABLE IF NOT EXISTS `position` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '岗位ID',
  `name` VARCHAR(50) NOT NULL COMMENT '岗位名称',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位表';

-- 4. 目标表
CREATE TABLE IF NOT EXISTS `goal` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '目标ID',
  `title` VARCHAR(100) NOT NULL COMMENT '目标标题',
  `description` TEXT COMMENT '目标描述',
  `type` ENUM('company','department','personal') NOT NULL COMMENT '目标类型',
  `owner_id` INT NOT NULL COMMENT '目标负责人',
  `department_id` INT DEFAULT NULL COMMENT '所属部门',
  `parent_id` INT DEFAULT NULL COMMENT '上级目标ID',
  `start_date` DATE DEFAULT NULL COMMENT '开始日期',
  `end_date` DATE DEFAULT NULL COMMENT '结束日期',
  `progress` DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
  `status` TINYINT DEFAULT 1 COMMENT '状态 1进行中 0已完成',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='目标表';

-- 5. 绩效考核表
CREATE TABLE IF NOT EXISTS `performance` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '考核ID',
  `user_id` INT NOT NULL COMMENT '被考核员工ID',
  `goal_id` INT DEFAULT NULL COMMENT '关联目标ID',
  `score` DECIMAL(5,2) DEFAULT NULL COMMENT '考核得分',
  `comment` TEXT COMMENT '考核评语',
  `period` VARCHAR(20) NOT NULL COMMENT '考核周期',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='绩效考核表';

-- 6. 项目表
CREATE TABLE IF NOT EXISTS `project` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
  `name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `description` TEXT COMMENT '项目描述',
  `owner_id` INT NOT NULL COMMENT '项目负责人',
  `start_date` DATE DEFAULT NULL COMMENT '开始日期',
  `end_date` DATE DEFAULT NULL COMMENT '结束日期',
  `status` TINYINT DEFAULT 1 COMMENT '状态 1进行中 0已完成',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';

-- 7. 项目成员表
CREATE TABLE IF NOT EXISTS `project_member` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `project_id` INT NOT NULL COMMENT '项目ID',
  `user_id` INT NOT NULL COMMENT '成员员工ID',
  `role` VARCHAR(50) DEFAULT NULL COMMENT '项目角色',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';

-- 8. 任务表
CREATE TABLE IF NOT EXISTS `task` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '任务ID',
  `project_id` INT NOT NULL COMMENT '所属项目ID',
  `title` VARCHAR(100) NOT NULL COMMENT '任务标题',
  `description` TEXT COMMENT '任务描述',
  `assignee_id` INT DEFAULT NULL COMMENT '负责人ID',
  `priority_id` INT DEFAULT NULL COMMENT '优先级ID',
  `status` VARCHAR(20) DEFAULT 'todo' COMMENT '状态(todo/doing/done)',
  `start_date` DATE DEFAULT NULL COMMENT '开始日期',
  `due_date` DATE DEFAULT NULL COMMENT '截止日期',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- 9. 文件表
CREATE TABLE IF NOT EXISTS `project_file` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
  `project_id` INT NOT NULL COMMENT '所属项目ID',
  `task_id` INT DEFAULT NULL COMMENT '所属任务ID',
  `filename` VARCHAR(255) NOT NULL COMMENT '文件名',
  `filepath` VARCHAR(255) NOT NULL COMMENT '文件路径',
  `uploader_id` INT NOT NULL COMMENT '上传人ID',
  `uploaded_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目文件表';

-- 10. 标签表
CREATE TABLE IF NOT EXISTS `project_tag` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
  `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
  `color` VARCHAR(20) DEFAULT NULL COMMENT '标签颜色'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 11. 任务标签关联表
CREATE TABLE IF NOT EXISTS `task_tag` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `task_id` INT NOT NULL COMMENT '任务ID',
  `tag_id` INT NOT NULL COMMENT '标签ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务标签关联表';

-- 12. 优先级表
CREATE TABLE IF NOT EXISTS `project_priority` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '优先级ID',
  `name` VARCHAR(20) NOT NULL COMMENT '优先级名称',
  `level` INT NOT NULL COMMENT '优先级等级(数字越大优先级越高)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优先级表';

-- 为目标表添加权重字段（如果不存在）
ALTER TABLE `goal` ADD COLUMN IF NOT EXISTS `weight` DECIMAL(5,2) DEFAULT 0.00 COMMENT '目标权重' AFTER `progress`;

-- 插入测试数据
-- 插入部门数据
INSERT IGNORE INTO `department` (`id`, `name`, `parent_id`) VALUES
(1, '技术部', NULL),
(2, '产品部', NULL),
(3, '市场部', NULL),
(4, '前端组', 1),
(5, '后端组', 1);

-- 插入用户数据
INSERT IGNORE INTO `user` (`id`, `username`, `realname`, `password`, `email`, `phone`, `department_id`, `position_id`, `status`) VALUES
(1, 'admin', '管理员', 'admin123', '<EMAIL>', '13800138000', 1, 1, 1),
(2, 'zhangsan', '张三', 'password123', '<EMAIL>', '13800138001', 1, 2, 1),
(3, 'lisi', '李四', 'password123', '<EMAIL>', '13800138002', 2, 3, 1),
(4, 'wangwu', '王五', 'password123', '<EMAIL>', '13800138003', 3, 4, 1),
(5, 'zhaoliu', '赵六', 'password123', '<EMAIL>', '13800138004', 1, 2, 1);

-- 插入目标数据
INSERT IGNORE INTO `goal` (`id`, `title`, `description`, `type`, `owner_id`, `department_id`, `parent_id`, `start_date`, `end_date`, `progress`, `weight`, `status`) VALUES
(1, '【行动】让OKR成为月度管理方式', '建立完善的OKR管理体系，提升团队目标管理效率', 'company', 1, NULL, NULL, '2024-01-01', '2024-12-31', 0, 30, 1),
(2, 'ERP业务系统优化改进Workflow OKR项目', '优化ERP系统工作流程，提升业务处理效率', 'department', 2, 1, NULL, '2024-02-01', '2024-08-31', 10, 25, 1),
(3, '消费者体验-100%顾客满意度调查（AI/VR技术）', '通过AI和VR技术提升消费者体验，实现100%顾客满意度', 'department', 3, 2, NULL, '2024-03-01', '2024-09-30', 40, 20, 1),
(4, '【行动】目标与实现的OKR体系建设', '建立目标与实现的完整OKR体系', 'company', 1, NULL, 1, '2024-01-15', '2024-06-30', 30, 15, 1),
(5, '年度方案（OKR/KPI/AI）等新方案的制定', '制定年度OKR、KPI和AI相关的新方案', 'department', 4, 3, NULL, '2024-01-01', '2024-03-31', 25, 10, 1),
(6, '有效的员工激励机制，提高OKR体系的执行效果', '建立有效的员工激励机制，提升OKR执行效果', 'personal', 2, 1, NULL, '2024-02-01', '2024-12-31', 65, 20, 1),
(7, '【行动】让团队成员OKR制定和执行', '指导团队成员制定和执行个人OKR', 'personal', 5, 1, NULL, '2024-01-01', '2024-12-31', 0, 15, 1),
(8, '建立OKR考核体系和奖惩机制', '建立完善的OKR考核体系和相应的奖惩机制', 'company', 1, NULL, NULL, '2024-04-01', '2024-10-31', 20, 25, 1),
(9, '年度工作计划制定', '制定详细的年度工作计划', 'personal', 3, 2, NULL, '2024-01-01', '2024-02-29', 70, 15, 1),
(10, '年度目标分解与执行', '将年度目标分解为季度和月度目标并执行', 'department', 4, 3, NULL, '2024-01-01', '2024-12-31', 30, 20, 1);
