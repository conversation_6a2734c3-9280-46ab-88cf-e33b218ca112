<template>
  <div style="padding: 24px;">
    <h3>工作台管理</h3>
    <el-table :data="taskList" style="width: 100%" @row-click="openTaskDetail">
      <el-table-column prop="id" label="任务ID" />
      <el-table-column prop="title" label="任务标题" />
      <el-table-column prop="project_id" label="所属项目ID" />
      <el-table-column prop="assignee_id" label="负责人ID" />
      <el-table-column prop="status" label="状态" />
      <el-table-column prop="due_date" label="截止日期" />
    </el-table>

    <!-- 任务详情对话框 -->
    <TaskDetailModal
      v-model="showTaskDetail"
      :task-id="selectedTaskId"
      @task-updated="handleTaskUpdated"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import TaskDetailModal from '../components/TaskDetailModal.vue';

const taskList = ref([]);
const showTaskDetail = ref(false);
const selectedTask = ref({});
const selectedTaskId = ref(null);

const fetchTasks = async () => {
  try {
    // 实际可根据登录用户过滤
    const res = await axios.get('/api/task');
    taskList.value = res.data;
  } catch (error) {
    console.error('获取任务列表失败:', error);
    // 使用模拟数据
    taskList.value = [
      {
        id: 1,
        title: '【示例】部门例会',
        project_id: 1,
        assignee_id: 1,
        status: 'doing',
        due_date: '2024-06-07',
        creator: {
          name: '张三',
          department: '技术部',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        }
      }
    ];
  }
};

const openTaskDetail = (task) => {
  selectedTask.value = task;
  selectedTaskId.value = task.id;
  showTaskDetail.value = true;
};

const handleTaskUpdated = (updatedTask) => {
  const index = taskList.value.findIndex(t => t.id === updatedTask.id);
  if (index !== -1) {
    taskList.value[index] = { ...taskList.value[index], ...updatedTask };
  }
};

onMounted(fetchTasks);
</script> 