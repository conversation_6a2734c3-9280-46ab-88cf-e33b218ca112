 import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

// 全局错误处理 - 忽略 ResizeObserver 错误
const originalError = console.error;
console.error = (...args) => {
  if (
    args[0]?.includes?.('ResizeObserver loop completed with undelivered notifications') ||
    args[0]?.includes?.('ResizeObserver loop limit exceeded') ||
    args[0]?.message?.includes?.('ResizeObserver')
  ) {
    return;
  }
  originalError(...args);
};

// 全局错误处理 - 捕获未处理的错误
window.addEventListener('error', (event) => {
  if (event.message?.includes('ResizeObserver')) {
    event.preventDefault();
    return false;
  }
});

// 全局 Promise 错误处理
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason?.message?.includes('ResizeObserver')) {
    event.preventDefault();
    return false;
  }
});

const app = createApp(App);
app.use(router);
app.use(ElementPlus, {
  locale: zhCn,
});
app.mount('#app');