# 目标管理页面功能测试清单

## 测试环境
- 前端服务：http://localhost:8080
- 后端服务：http://localhost:3000
- 目标页面：http://localhost:8080/goal

## 基础功能测试

### 1. 页面加载测试
- [ ] 页面能正常加载，无JavaScript错误
- [ ] 左侧导航栏正常显示
- [ ] 目标列表能正常加载数据
- [ ] 用户列表能正常加载（用于负责人选择）

### 2. 导航功能测试
- [ ] 左侧导航栏各项目能正常点击切换
  - [ ] 目标（默认选中）
  - [ ] 我的活动
  - [ ] 我的目标
  - [ ] 公司目标
  - [ ] 同事目标
  - [ ] 智囊团
- [ ] 顶部标签页能正常切换
  - [ ] 目标
  - [ ] 目标
  - [ ] 分析报告

### 3. 搜索和筛选功能测试
- [ ] 搜索框能正常输入和搜索
- [ ] 状态筛选下拉框正常工作
  - [ ] 全部
  - [ ] 进行中
  - [ ] 已完成
- [ ] 类型筛选下拉框正常工作
  - [ ] 全部
  - [ ] 公司目标
  - [ ] 部门目标
  - [ ] 个人目标
- [ ] 负责人筛选下拉框正常工作
- [ ] 筛选条件能正确过滤数据

### 4. 目标列表显示测试
- [ ] 目标列表正常显示
- [ ] 目标信息完整显示
  - [ ] 目标名称和描述
  - [ ] 进度条和百分比
  - [ ] 负责人头像和姓名
  - [ ] 预计完成时间
  - [ ] 权重
  - [ ] 目标状态标签
- [ ] 操作按钮正常显示（编辑、删除）

### 5. 分页功能测试
- [ ] 分页组件正常显示
- [ ] 页码切换正常工作
- [ ] 每页显示数量切换正常工作
- [ ] 总数统计正确

## 新建目标功能测试

### 6. 新建目标弹窗测试
- [ ] 点击"新建目标"按钮能打开弹窗
- [ ] 弹窗表单正常显示
- [ ] 表单字段完整
  - [ ] 目标名称（必填）
  - [ ] 目标描述
  - [ ] 目标周期（日期范围选择器）
  - [ ] 负责人下拉选择
  - [ ] 目标类型下拉选择
  - [ ] 目标权重数字输入
  - [ ] 关键结果动态列表

### 7. 表单验证测试
- [ ] 必填字段验证正常
- [ ] 目标名称不能为空
- [ ] 目标类型必须选择
- [ ] 负责人必须选择
- [ ] 权重数值范围验证（0-100）

### 8. 关键结果功能测试
- [ ] 默认有一个关键结果输入框
- [ ] 点击"添加关键结果"能增加新的输入框
- [ ] 删除按钮能正常删除关键结果（保留至少一个）
- [ ] 关键结果内容能正常输入

### 9. 目标创建提交测试
- [ ] 表单填写完整后能正常提交
- [ ] 提交时显示加载状态
- [ ] 创建成功后显示成功提示
- [ ] 弹窗自动关闭
- [ ] 目标列表自动刷新显示新创建的目标
- [ ] 表单重置为初始状态

## 目标操作功能测试

### 10. 编辑目标测试
- [ ] 点击编辑按钮有响应（当前显示"编辑功能待实现"提示）

### 11. 删除目标测试
- [ ] 点击删除按钮显示确认对话框
- [ ] 确认删除后目标被删除
- [ ] 取消删除后目标保持不变
- [ ] 删除成功后显示成功提示
- [ ] 目标列表自动刷新

## API接口测试

### 12. 后端API测试
- [ ] GET /api/goal 返回目标列表
- [ ] GET /api/user 返回用户列表
- [ ] POST /api/goal 创建新目标
- [ ] DELETE /api/goal/:id 删除目标
- [ ] API返回数据格式正确
- [ ] 错误处理正常

## 响应式设计测试

### 13. 移动端适配测试
- [ ] 在小屏幕设备上布局正常
- [ ] 侧边栏在移动端能正常显示
- [ ] 筛选条件在移动端布局合理
- [ ] 表格在移动端能正常滚动

## 性能测试

### 14. 页面性能测试
- [ ] 页面加载速度正常
- [ ] 数据筛选响应及时
- [ ] 大量数据时列表渲染正常
- [ ] 内存使用合理，无内存泄漏

## 浏览器兼容性测试

### 15. 浏览器测试
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] Safari浏览器正常
- [ ] Edge浏览器正常

## 测试结果记录

测试日期：_______
测试人员：_______
测试环境：_______

发现的问题：
1. 
2. 
3. 

总体评价：
- [ ] 功能完整，可以发布
- [ ] 存在小问题，需要修复后发布
- [ ] 存在重大问题，需要重新开发
