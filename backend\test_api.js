const http = require('http');

function testAPI(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function runTests() {
  try {
    console.log('测试目标API...');
    const goalData = await testAPI('/api/goal');
    console.log('目标API响应:', goalData);

    console.log('\n测试用户API...');
    const userData = await testAPI('/api/user');
    console.log('用户API响应:', userData);

  } catch (error) {
    console.error('API测试失败:', error.message);
  }
}

runTests();
