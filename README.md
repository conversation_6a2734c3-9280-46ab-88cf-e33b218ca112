# 企业ORK系统

## 简介
本系统为企业级目标管理与绩效考核平台，支持目标管理、绩效考核、员工管理、项目管理等核心功能。前端采用Vue3+Element Plus，后端采用Node.js+Express，数据库为MySQL。

## 主要功能
- 目标管理（公司/部门/个人目标分解与追踪，支持OKR管理）
- 绩效考核（考核模板、打分、反馈、归档）
- 员工管理（信息、组织架构、岗位）
- 项目管理（立项、分配、进度、成员）
- 任务看板（可视化任务管理，支持拖拽）
- 工作台（现代化界面，任务统计、日历集成）

## 技术栈
- 前端：Vue3、Element Plus、Axios、Vue Router、Pinia
- 后端：Node.js、Express、MySQL2、JWT
- 数据库：MySQL

## 目录结构
```
ork企业/
├── backend/           # Node.js后端
│   ├── src/
│   ├── config/
│   └── package.json
├── frontend/          # Vue前端
│   ├── src/
│   └── package.json
├── docs/              # 设计文档、数据库脚本
│   └── db/
└── README.md
```

## 启动方式
1. 安装依赖
   - 后端：`cd backend && npm install`
   - 前端：`cd frontend && npm install`
2. 启动服务
   - 后端：`npm start`
   - 前端：`npm run serve`
3. 数据库
   - 使用 `docs/db/ork_init.sql` 初始化数据库
   - 如需回滚，使用 `docs/db/ork_init_rollback.sql`
   - 如需添加目标权重字段，使用 `docs/db/goal_weight_update.sql`

## 功能特性

### 目标管理
- 🎯 现代化的目标管理界面，支持OKR管理模式
- 📊 目标进度可视化，支持权重设置
- 👥 多维度目标分类（公司/部门/个人目标）
- 🔍 强大的搜索和筛选功能
- ✅ 关键结果管理，支持动态添加
- 📅 目标周期管理，支持日期范围设置

### 项目管理
- 📋 项目列表管理，支持多种视图
- 📈 项目详情页面，包含任务、甘特图、成员、文档、设置
- 🎨 任务看板，支持拖拽式任务管理
- 👨‍💼 项目成员管理和权限控制
- 📁 项目文档管理和文件共享

### 工作台
- 🏠 现代化工作台界面，类似Worktile风格
- 📊 任务统计和进度展示
- 📅 日历集成，任务日程管理
- 🔔 消息通知和提醒功能

## 数据库配置
请在 `backend/config/db.js` 中配置数据库连接信息。

---
如需定制功能或有其他需求，请联系开发者。 