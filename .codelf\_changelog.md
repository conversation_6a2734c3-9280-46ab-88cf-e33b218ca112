## 2024-05-20 16:00:00

### 1. 项目初始化与目录结构搭建
**Change Type**: feature
> **Purpose**: 初始化企业ORK系统项目结构，明确前后端分离、数据库脚本、文档等目录。
> **Detailed Description**: 创建backend、frontend、docs、README等基础目录和文件。
> **Reason for Change**: 项目启动，规范结构。
> **Impact Scope**: 全局

   ```
   root
   - backend // 新增
   - frontend // 新增
   - docs // 新增
   - README.md // 新增
   ```

### 2. 数据库表结构设计与脚本实现
**Change Type**: feature
> **Purpose**: 设计并实现员工、部门、岗位、目标、绩效、项目等核心表结构。
> **Detailed Description**: 编写ork_init.sql和ork_init_rollback.sql，支持初始化和回滚。
> **Reason for Change**: 支撑业务核心数据。
> **Impact Scope**: 数据库

   ```
   docs/db/ork_init.sql // 新增
   docs/db/ork_init_rollback.sql // 新增
   ```

### 3. 后端Express基础与API实现
**Change Type**: feature
> **Purpose**: 实现Node.js后端基础，完成各模块增删改查API。
> **Detailed Description**: 编写app.js、各路由、控制器、数据库连接池。
> **Reason for Change**: 支持前端数据交互。
> **Impact Scope**: backend/src

   ```
   backend/src/app.js // 新增
   backend/src/controllers/*.js // 新增
   backend/src/routes/*.js // 新增
   backend/src/db.js // 新增
   ```

### 4. 前端Vue3页面与数据联动
**Change Type**: feature
> **Purpose**: 实现主框架、路由、各模块页面，支持与后端API联动。
> **Detailed Description**: 编写App.vue、router、views等，axios请求API。
> **Reason for Change**: 实现业务可视化。
> **Impact Scope**: frontend/src

   ```
   frontend/src/App.vue // 新增
   frontend/src/main.js // 新增
   frontend/src/router/index.js // 新增
   frontend/src/views/*.vue // 新增
   frontend/vue.config.js // 新增
   ```

## 2024-12-19 12:00:00

### 1. UI界面重构为Worktile风格工作台

**Change Type**: feature

> **Purpose**: 将传统的横向导航布局重构为现代化的左侧可收缩导航+工作台布局，提升用户体验
> **Detailed Description**: 完全重新设计App.vue主框架和Home.vue工作台页面，实现类似Worktile的现代化界面风格，包含可收缩侧边栏、顶部搜索栏、任务列表、日历、统计卡片等功能模块
> **Reason for Change**: 原有的横向导航布局不够现代化，用户体验较差，需要升级为更专业的项目管理界面
> **Impact Scope**: 前端整体布局和用户交互体验
> **API Changes**: 无API变更
> **Configuration Changes**: 无配置变更
> **Performance Impact**: 界面渲染性能优化，响应式布局提升

   ```
   frontend/src/
   - App.vue           // refact 重构主框架布局，添加可收缩侧边栏和顶部导航
   - views/
     - Home.vue        // refact 重构工作台页面，添加任务列表、日历、统计等功能
   ```

### 2. 新增工作台核心功能模块

**Change Type**: feature

> **Purpose**: 在工作台页面实现任务管理、日历视图、项目统计等核心功能
> **Detailed Description**: 添加任务列表筛选、优先级显示、日历集成、统计数据展示、最近项目列表等功能模块，提供完整的工作台体验
> **Reason for Change**: 提供类似专业项目管理工具的功能体验
> **Impact Scope**: 工作台页面功能完整性
> **API Changes**: 暂时使用模拟数据，后续需要对接后端API
> **Configuration Changes**: 无
> **Performance Impact**: 增加了页面功能复杂度，但通过合理的组件设计保持良好性能

   ```
   frontend/src/views/Home.vue
   - 任务列表功能      // add 支持按状态筛选的任务列表
   - 日历集成         // add Element Plus日历组件集成
   - 统计数据展示     // add 今日任务统计卡片
   - 最近项目列表     // add 项目进度展示
   ```

### 3. 重构项目管理页面为现代化列表视图

**Change Type**: feature

> **Purpose**: 将简单的项目表格重构为功能完整的项目管理界面
> **Detailed Description**: 实现项目列表的标签页切换、搜索筛选、状态管理、项目创建等功能，提供类似专业项目管理工具的体验
> **Reason for Change**: 原有项目页面功能过于简单，需要提供完整的项目管理功能
> **Impact Scope**: 项目管理模块的用户体验和功能完整性
> **API Changes**: 暂时使用模拟数据，保持与后端API的兼容性
> **Configuration Changes**: 无
> **Performance Impact**: 优化了表格渲染和交互性能

   ```
   frontend/src/views/Project.vue
   - 标签页导航       // add 我的项目/我参与的/项目模板
   - 搜索筛选功能     // add 项目名称搜索、状态筛选、负责人筛选
   - 项目创建对话框   // add 新建项目功能
   - 项目操作菜单     // add 编辑/归档/删除操作
   - 分页功能        // add 项目列表分页
   ```

### 4. 新增任务看板页面

**Change Type**: feature

> **Purpose**: 实现类似Worktile的任务看板功能，支持拖拽式任务管理
> **Detailed Description**: 创建全新的任务看板页面，包含待开始、进行中、测试中、已完成四个状态列，支持任务拖拽移动、任务创建、任务详情查看等功能
> **Reason for Change**: 提供可视化的任务管理方式，提升项目管理效率
> **Impact Scope**: 新增核心功能模块
> **API Changes**: 新增任务看板路由 /task-board
> **Configuration Changes**: 安装vuedraggable@next依赖包
> **Performance Impact**: 新增页面，增加了应用整体功能复杂度

   ```
   frontend/src/views/TaskBoard.vue  // add 全新任务看板页面
   frontend/src/router/index.js      // add 任务看板路由
   frontend/src/App.vue              // add 任务看板导航入口
   frontend/package.json             // add vuedraggable@next依赖
   ```

### 5. 创建完整的项目详情页面系统

**Change Type**: feature

> **Purpose**: 实现类似Worktile的项目详情页面，包含五个核心模块
> **Detailed Description**: 创建项目详情页面，包含任务列表、甘特图、项目成员、文档管理、项目设置五个标签页，每个模块都有完整的功能实现
> **Reason for Change**: 提供完整的项目管理功能，满足企业级项目管理需求
> **Impact Scope**: 新增核心项目管理功能模块
> **API Changes**: 新增项目详情路由 /project/:id
> **Configuration Changes**: 无
> **Performance Impact**: 增加了应用功能复杂度，但通过模块化设计保持良好性能

   ```
   frontend/src/views/ProjectDetail.vue                    // add 项目详情主页面
   frontend/src/components/project/TaskList.vue           // add 任务列表组件
   frontend/src/components/project/GanttChart.vue         // add 甘特图组件
   frontend/src/components/project/ProjectMembers.vue     // add 项目成员管理组件
   frontend/src/components/project/ProjectDocuments.vue   // add 项目文档管理组件
   frontend/src/components/project/ProjectSettings.vue    // add 项目设置组件
   frontend/src/router/index.js                           // add 项目详情路由
   ```

#### 5.1 任务列表模块功能
- ✅ 任务创建、编辑、删除
- ✅ 任务状态管理（待处理/进行中/已完成）
- ✅ 优先级设置（高/中/低）
- ✅ 负责人分配
- ✅ 截止日期管理
- ✅ 搜索和筛选功能
- ✅ 列表视图和卡片视图切换
- ✅ 任务统计展示

#### 5.2 甘特图模块功能
- ✅ 任务时间轴可视化
- ✅ 任务依赖关系展示
- ✅ 项目进度跟踪
- ✅ 时间视图切换（日/周/月）
- ✅ 任务拖拽调整
- ✅ 层级任务管理

#### 5.3 项目成员模块功能
- ✅ 成员列表展示
- ✅ 成员邀请功能
- ✅ 角色权限管理
- ✅ 成员状态显示（在线/离线/忙碌）
- ✅ 成员工作量统计
- ✅ 成员搜索和筛选

#### 5.4 项目文档模块功能
- ✅ 文档上传下载
- ✅ 文件夹管理
- ✅ 文档预览功能
- ✅ 文档搜索
- ✅ 列表和网格视图
- ✅ 文档权限控制

#### 5.5 项目设置模块功能
- ✅ 基本信息设置
- ✅ 权限管理配置
- ✅ 通知设置
- ✅ 第三方集成
- ✅ 危险操作（归档/转移/删除）

## 2025-01-06 17:30:00

### 1. 目标管理页面完全重构为现代化界面

**Change Type**: refactor

> **Purpose**: 将简单的目标表格页面重构为功能完整的现代化目标管理界面，提供类似专业项目管理工具的用户体验
> **Detailed Description**: 完全重新设计Goal.vue页面，实现左侧导航栏（目标/我的活动/我的目标/公司目标/同事目标/智囊团）、标签页切换、搜索筛选、目标列表展示、新建目标弹窗等功能。目标列表包含进度条、负责人头像、权重、状态等信息展示，支持编辑和删除操作
> **Reason for Change**: 原有的目标页面过于简单，只有基础的表格展示，无法满足企业级目标管理的需求，需要提供完整的OKR管理功能
> **Impact Scope**: 目标管理模块的用户体验和功能完整性，后端API增强
> **API Changes**: 增强目标列表API，支持关联用户信息查询；新增权重字段支持
> **Configuration Changes**: 新增数据库权重字段更新脚本
> **Performance Impact**: 优化了页面渲染性能，增加了响应式设计支持

   ```
   frontend/src/views/Goal.vue                    // refact 完全重构目标管理页面
   backend/src/controllers/goal.js               // refact 增强API支持权重字段和用户关联
   docs/db/goal_weight_update.sql                // add 权重字段数据库更新脚本
   docs/db/ork_init.sql                          // refact 添加测试数据和权重字段
   backend/update_db.js                          // add 数据库更新工具脚本
   backend/test_api.js                           // add API测试脚本
   backend/test_db.js                            // add 数据库连接测试脚本
   ```

#### 1.1 前端功能特性
- ✅ 左侧导航栏（目标/我的活动/我的目标/公司目标/同事目标/智囊团）
- ✅ 标签页切换（目标/目标/分析报告）
- ✅ 搜索和筛选功能（状态、类型、负责人）
- ✅ 目标列表展示（进度条、负责人头像、权重、状态）
- ✅ 新建目标弹窗（目标名称、描述、周期、负责人、类型、权重、关键结果）
- ✅ 目标操作（编辑、删除）
- ✅ 分页功能
- ✅ 响应式设计

#### 1.2 后端API增强
- ✅ 目标列表API支持关联用户信息查询
- ✅ 新增权重字段支持
- ✅ 创建和更新API支持权重参数
- ✅ 数据库测试和API测试脚本

#### 1.3 数据库优化
- ✅ 添加权重字段到目标表
- ✅ 插入测试数据（用户和目标）
- ✅ 提供数据库更新脚本

## 2025-01-06 17:40:00

### 2. 优化目标管理页面布局和导航结构

**Change Type**: improvement

> **Purpose**: 根据用户反馈优化页面布局，实现动态菜单切换和更合理的界面结构
> **Detailed Description**: 移除冗余的页面标题，将标签页作为主导航，左侧菜单根据选择的标签页动态变化。目标标签页显示目标相关菜单（目标、我负责的、我关注的、全部目标、目标关系网、智囊团），分析报告标签页显示报告相关菜单（分析报告、运营报告、进度报告、评分报告）
> **Reason for Change**: 用户指出原有的页面标题是冗余的，标签页和左侧菜单的关系需要更清晰，应该实现父子菜单的动态切换
> **Impact Scope**: 目标管理页面的整体布局和用户交互体验
> **API Changes**: 无API变更
> **Configuration Changes**: 无配置变更
> **Performance Impact**: 优化了页面结构，提升了用户体验

   ```
   frontend/src/views/Goal.vue
   - 页面标题部分           // del 移除冗余的页面标题
   - 标签页导航            // refact 优化标签页设计，添加图标
   - 左侧菜单             // refact 实现动态菜单切换
   - 工具栏               // refact 将新建目标按钮移至工具栏
   - 分析报告内容区域       // add 新增分析报告页面内容
   ```

#### 2.1 界面结构优化
- ✅ 移除页面顶部标题，简化界面
- ✅ 标签页作为主导航（目标、目标、分析报告）
- ✅ 左侧菜单根据标签页动态切换
- ✅ 工具栏集成搜索、筛选和操作按钮

#### 2.2 动态菜单实现
- ✅ 目标标签页菜单：目标、我负责的、我关注的、全部目标、目标关系网、智囊团
- ✅ 分析报告标签页菜单：分析报告、运营报告、进度报告、评分报告
- ✅ 菜单切换时自动重置左侧导航状态

#### 2.3 分析报告功能
- ✅ 分析报告页面框架
- ✅ 整体和更新进度子标签页
- ✅ 关键结果统计展示
- ✅ 响应式设计适配

## {datetime: YYYY-MM-DD HH:mm:ss}

### 3. {function simple description}

**Change Type**: {type: feature/fix/improvement/refactor/docs/test/build}

> **Purpose**: {function purpose}
> **Detailed Description**: {function detailed description}
> **Reason for Change**: {why this change is needed}
> **Impact Scope**: {other modules or functions that may be affected by this change}
> **API Changes**: {if there are API changes, detail the old and new APIs}
> **Configuration Changes**: {changes to environment variables, config files, etc.}
> **Performance Impact**: {impact of the change on system performance}

   ```
   root
   - pkg    // {type: add/del/refact/-} {The role of a folder}
    - utils // {type: add/del/refact} {The function of the file}
   - xxx    // {type: add/del/refact} {The function of the file}
   ```

...