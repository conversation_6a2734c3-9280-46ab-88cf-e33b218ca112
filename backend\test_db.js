const pool = require('./src/db');

async function testDatabase() {
  try {
    console.log('测试数据库连接...');
    const [rows] = await pool.query('SELECT 1 as test');
    console.log('数据库连接成功:', rows);
    
    console.log('测试目标表...');
    const [goals] = await pool.query('SELECT * FROM goal LIMIT 5');
    console.log('目标数据:', goals);
    
    console.log('测试用户表...');
    const [users] = await pool.query('SELECT * FROM user LIMIT 5');
    console.log('用户数据:', users);
    
  } catch (error) {
    console.error('数据库测试失败:', error.message);
  } finally {
    process.exit(0);
  }
}

testDatabase();
