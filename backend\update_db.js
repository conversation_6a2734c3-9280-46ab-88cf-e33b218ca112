const mysql = require('mysql2/promise');
const dbConfig = require('./config/db');

async function updateDatabase() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 检查并添加权重字段
    try {
      await connection.execute(`
        ALTER TABLE goal ADD COLUMN weight DECIMAL(5,2) DEFAULT 0.00 COMMENT '目标权重' AFTER progress
      `);
      console.log('权重字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('权重字段已存在，跳过添加');
      } else {
        throw error;
      }
    }

    // 插入测试数据
    const testUsers = [
      [1, 'admin', '管理员', 'admin123', '<EMAIL>', '13800138000', 1, 1, 1],
      [2, 'zhangsan', '张三', 'password123', 'zhang<PERSON>@company.com', '13800138001', 1, 2, 1],
      [3, 'lisi', '李四', 'password123', '<EMAIL>', '13800138002', 2, 3, 1],
      [4, 'wangwu', '王五', 'password123', '<EMAIL>', '13800138003', 3, 4, 1],
      [5, 'zhaoliu', '赵六', 'password123', '<EMAIL>', '13800138004', 1, 2, 1]
    ];

    for (const user of testUsers) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO user (id, username, realname, password, email, phone, department_id, position_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
          user
        );
      } catch (error) {
        console.log(`用户 ${user[2]} 插入失败或已存在:`, error.message);
      }
    }

    const testGoals = [
      [1, '【行动】让OKR成为月度管理方式', '建立完善的OKR管理体系，提升团队目标管理效率', 'company', 1, null, null, '2024-01-01', '2024-12-31', 0, 30, 1],
      [2, 'ERP业务系统优化改进Workflow OKR项目', '优化ERP系统工作流程，提升业务处理效率', 'department', 2, 1, null, '2024-02-01', '2024-08-31', 10, 25, 1],
      [3, '消费者体验-100%顾客满意度调查（AI/VR技术）', '通过AI和VR技术提升消费者体验，实现100%顾客满意度', 'department', 3, 2, null, '2024-03-01', '2024-09-30', 40, 20, 1],
      [4, '【行动】目标与实现的OKR体系建设', '建立目标与实现的完整OKR体系', 'company', 1, null, 1, '2024-01-15', '2024-06-30', 30, 15, 1],
      [5, '年度方案（OKR/KPI/AI）等新方案的制定', '制定年度OKR、KPI和AI相关的新方案', 'department', 4, 3, null, '2024-01-01', '2024-03-31', 25, 10, 1]
    ];

    for (const goal of testGoals) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO goal (id, title, description, type, owner_id, department_id, parent_id, start_date, end_date, progress, weight, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          goal
        );
      } catch (error) {
        console.log(`目标 ${goal[1]} 插入失败或已存在:`, error.message);
      }
    }

    console.log('数据库更新完成');
    
  } catch (error) {
    console.error('数据库更新失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

updateDatabase();
