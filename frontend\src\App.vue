<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <div class="top-header">
      <div class="header-left">
        <el-button
          :icon="isCollapsed ? 'Expand' : 'Fold'"
          @click="toggleSidebar"
          text
          class="sidebar-toggle"
        />
        <span class="logo">OKR企业管理</span>
      </div>
      <div class="header-center">
        <el-input
          v-model="searchText"
          placeholder="搜索项目、任务..."
          prefix-icon="Search"
          class="search-input"
        />
      </div>
      <div class="header-right">
        <el-button :icon="Bell" circle />
        <el-button :icon="QuestionFilled" circle />
        <el-dropdown>
          <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人设置</el-dropdown-item>
              <el-dropdown-item>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主体容器 -->
    <div class="main-container">
      <!-- 左侧导航栏 -->
      <div class="sidebar" :class="{ collapsed: isCollapsed }">
        <div class="sidebar-content">
          <!-- 工作台 -->
          <div class="nav-section">
            <div class="nav-item" :class="{ active: $route.path === '/' }" @click="$router.push('/')">
              <el-icon><House /></el-icon>
              <span v-show="!isCollapsed">工作台</span>
            </div>
          </div>

          <!-- 项目管理 -->
          <div class="nav-section">
            <div class="section-title" v-show="!isCollapsed">项目管理</div>
            <div class="nav-item" :class="{ active: $route.path === '/project' }" @click="$router.push('/project')">
              <el-icon><FolderOpened /></el-icon>
              <span v-show="!isCollapsed">项目</span>
            </div>
            <div class="nav-item" :class="{ active: $route.path === '/task-board' }" @click="$router.push('/task-board')">
              <el-icon><Grid /></el-icon>
              <span v-show="!isCollapsed">任务看板</span>
            </div>
            <div class="nav-item" :class="{ active: $route.path === '/goal' }" @click="$router.push('/goal')">
              <el-icon><Flag /></el-icon>
              <span v-show="!isCollapsed">目标</span>
            </div>
          </div>

          <!-- 人员管理 -->
          <div class="nav-section">
            <div class="section-title" v-show="!isCollapsed">人员管理</div>
            <div class="nav-item" :class="{ active: $route.path === '/user' }" @click="$router.push('/user')">
              <el-icon><User /></el-icon>
              <span v-show="!isCollapsed">员工</span>
            </div>
            <div class="nav-item" :class="{ active: $route.path === '/department' }" @click="$router.push('/department')">
              <el-icon><OfficeBuilding /></el-icon>
              <span v-show="!isCollapsed">部门</span>
            </div>
          </div>

          <!-- 绩效考核 -->
          <div class="nav-section">
            <div class="section-title" v-show="!isCollapsed">绩效考核</div>
            <div class="nav-item" :class="{ active: $route.path === '/performance' }" @click="$router.push('/performance')">
              <el-icon><TrendCharts /></el-icon>
              <span v-show="!isCollapsed">绩效</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="content-area">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  Bell,
  QuestionFilled,
  House,
  FolderOpened,
  Grid,
  Flag,
  User,
  OfficeBuilding,
  TrendCharts
} from '@element-plus/icons-vue'

// 响应式数据
const isCollapsed = ref(false)
const searchText = ref('')

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}
</script>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f6fa;
}

/* 顶部导航栏 */
.top-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  color: #606266;
}

.logo {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-input {
  width: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 主体容器 */
.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧导航栏 */
.sidebar {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-content {
  padding: 20px 0;
}

.nav-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 12px;
  color: #909399;
  padding: 0 20px 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-item {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #606266;
  gap: 12px;
}

.nav-item:hover {
  background: #f5f7fa;
  color: #409eff;
}

.nav-item.active {
  background: #ecf5ff;
  color: #409eff;
  border-right: 3px solid #409eff;
}

.nav-item .el-icon {
  font-size: 18px;
  min-width: 18px;
}

.nav-item span {
  font-size: 14px;
  white-space: nowrap;
}

/* 主内容区域 */
.content-area {
  flex: 1;
  overflow: auto;
  background: #f5f6fa;
}
</style>

<style>
body {
  margin: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: #f5f6fa;
}

* {
  box-sizing: border-box;
}
</style>