<template>
  <div class="goal-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <el-icon><Flag /></el-icon>
          目标
        </h2>
        <div class="project-selector">
          <el-select v-model="selectedProject" placeholder="选择项目">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">新建目标</el-button>
        <el-button :icon="Refresh" @click="refreshData">刷新</el-button>
      </div>
    </div>

    <!-- 主体容器 -->
    <div class="main-container">
      <!-- 左侧导航栏 -->
      <div class="sidebar">
        <div class="sidebar-content">
          <!-- 目标相关菜单 -->
          <div class="nav-section" v-if="activeTab === 'goals'">
            <div class="nav-item" :class="{ active: activeNav === 'goals' }" @click="activeNav = 'goals'">
              <el-icon><Flag /></el-icon>
              <span>目标</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'my-responsible' }" @click="activeNav = 'my-responsible'">
              <el-icon><User /></el-icon>
              <span>我负责的</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'my-focus' }" @click="activeNav = 'my-focus'">
              <el-icon><Star /></el-icon>
              <span>我关注的</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'all-goals' }" @click="activeNav = 'all-goals'">
              <el-icon><Grid /></el-icon>
              <span>全部目标</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'goal-network' }" @click="activeNav = 'goal-network'">
              <el-icon><Share /></el-icon>
              <span>目标关系网</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'think-tank' }" @click="activeNav = 'think-tank'">
              <el-icon><ChatDotRound /></el-icon>
              <span>智囊团</span>
            </div>
          </div>

          <!-- 分析报告菜单 -->
          <div class="nav-section" v-if="activeTab === 'analysis'">
            <div class="nav-item" :class="{ active: activeNav === 'analysis-report' }" @click="activeNav = 'analysis-report'">
              <el-icon><DataAnalysis /></el-icon>
              <span>分析报告</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'operation-report' }" @click="activeNav = 'operation-report'">
              <el-icon><Clock /></el-icon>
              <span>运营报告</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'progress-report' }" @click="activeNav = 'progress-report'">
              <el-icon><TrendCharts /></el-icon>
              <span>进度报告</span>
            </div>
            <div class="nav-item" :class="{ active: activeNav === 'score-report' }" @click="activeNav = 'score-report'">
              <el-icon><Document /></el-icon>
              <span>评分报告</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="content-area">
        <!-- 标签页 -->
        <div class="tabs-container">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="目标" name="goals">
              <template #label>
                <el-icon><Flag /></el-icon>
                <span>目标</span>
              </template>
            </el-tab-pane>
            <el-tab-pane label="分析报告" name="analysis">
              <template #label>
                <el-icon><DataAnalysis /></el-icon>
                <span>分析报告</span>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar-container">
          <div class="toolbar-left">
            <el-input
              v-model="searchText"
              placeholder="搜索目标名称、负责人、部门（按Enter搜索）"
              prefix-icon="Search"
              class="search-input"
              clearable
            />
          </div>
          <div class="toolbar-right">
            <el-select v-model="statusFilter" placeholder="状态" clearable class="filter-select">
              <el-option label="全部" value="" />
              <el-option label="进行中" value="1" />
              <el-option label="已完成" value="0" />
            </el-select>
            <el-select v-model="typeFilter" placeholder="类型" clearable class="filter-select">
              <el-option label="全部" value="" />
              <el-option label="公司目标" value="company" />
              <el-option label="部门目标" value="department" />
              <el-option label="个人目标" value="personal" />
            </el-select>
            <el-select v-model="ownerFilter" placeholder="负责人" clearable class="filter-select">
              <el-option label="全部" value="" />
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.realname"
                :value="user.id"
              />
            </el-select>

          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-main">
          <!-- 目标列表 -->
          <div class="table-container" v-if="activeTab === 'goals'">
            <el-table
              :data="filteredGoalList"
              style="width: 100%"
              v-loading="loading"
              empty-text="暂无数据"
            >
              <el-table-column label="目标" min-width="300">
                <template #default="{ row }">
                  <div class="goal-info">
                    <div class="goal-title">{{ row.title }}</div>
                    <div class="goal-description" v-if="row.description">{{ row.description }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="进度" width="120" align="center">
                <template #default="{ row }">
                  <div class="progress-container">
                    <el-progress
                      :percentage="row.progress"
                      :stroke-width="8"
                      :show-text="false"
                    />
                    <span class="progress-text">{{ row.progress }}%</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="负责人" width="120" align="center">
                <template #default="{ row }">
                  <div class="owner-info">
                    <el-avatar :size="32" :src="row.owner_avatar" />
                    <span class="owner-name">{{ row.owner_name || '未分配' }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="预计完成时间" width="140" align="center">
                <template #default="{ row }">
                  <span v-if="row.end_date">{{ formatDate(row.end_date) }}</span>
                  <span v-else class="text-gray">-</span>
                </template>
              </el-table-column>

              <el-table-column label="权重" width="80" align="center">
                <template #default="{ row }">
                  <span>{{ row.weight || '-' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="目标状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag
                    :type="row.status === 1 ? 'primary' : 'success'"
                    size="small"
                  >
                    {{ row.status === 1 ? '进行中' : '已完成' }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button type="text" size="small" @click="editGoal(row)">编辑</el-button>
                  <el-button type="text" size="small" @click="deleteGoal(row)" style="color: #f56c6c;">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="totalCount"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>

          <!-- 分析报告内容 -->
          <div class="analysis-container" v-if="activeTab === 'analysis'">
            <div class="analysis-content">
              <div class="analysis-header">
                <h3>{{ getAnalysisTitle() }}</h3>
                <div class="analysis-tabs">
                  <el-button
                    :type="analysisSubTab === 'overview' ? 'primary' : ''"
                    @click="analysisSubTab = 'overview'"
                  >
                    整体
                  </el-button>
                  <el-button
                    :type="analysisSubTab === 'progress' ? 'primary' : ''"
                    @click="analysisSubTab = 'progress'"
                  >
                    更新进度
                  </el-button>
                </div>
              </div>

              <div class="analysis-body">
                <div v-if="analysisSubTab === 'overview'" class="overview-content">
                  <div class="stats-cards">
                    <div class="stat-card">
                      <div class="stat-title">关键结果统计</div>
                      <div class="stat-content">
                        <div class="kr-stats">
                          <div class="kr-item">
                            <span class="kr-label">KR1</span>
                            <span class="kr-desc">可以完成</span>
                          </div>
                          <div class="kr-item">
                            <span class="kr-label">KR2</span>
                            <span class="kr-desc">有风险</span>
                          </div>
                          <div class="kr-item">
                            <span class="kr-label">KR3</span>
                            <span class="kr-desc">失败</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="analysisSubTab === 'progress'" class="progress-content">
                  <el-empty description="更新进度功能开发中..." />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建目标弹窗 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建目标"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="newGoal" :rules="goalRules" ref="goalFormRef" label-width="100px">
        <el-form-item label="目标名称" prop="title">
          <el-input v-model="newGoal.title" placeholder="请输入目标名称" />
        </el-form-item>

        <el-form-item label="目标描述" prop="description">
          <el-input
            v-model="newGoal.description"
            type="textarea"
            :rows="3"
            placeholder="请输入目标描述"
          />
        </el-form-item>

        <el-form-item label="目标周期" prop="dateRange">
          <el-date-picker
            v-model="newGoal.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="负责人" prop="owner_id">
          <el-select v-model="newGoal.owner_id" placeholder="请选择负责人" style="width: 100%">
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.realname"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="目标类型" prop="type">
          <el-select v-model="newGoal.type" placeholder="请选择目标类型" style="width: 100%">
            <el-option label="公司目标" value="company" />
            <el-option label="部门目标" value="department" />
            <el-option label="个人目标" value="personal" />
          </el-select>
        </el-form-item>

        <el-form-item label="目标权重" prop="weight">
          <el-input-number
            v-model="newGoal.weight"
            :min="0"
            :max="100"
            placeholder="请输入权重"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="关键结果">
          <div class="key-results">
            <div
              v-for="(result, index) in newGoal.keyResults"
              :key="index"
              class="key-result-item"
            >
              <el-input
                v-model="result.content"
                placeholder="请输入关键结果"
                style="flex: 1; margin-right: 8px;"
              />
              <el-button
                type="danger"
                :icon="Delete"
                circle
                size="small"
                @click="removeKeyResult(index)"
                v-if="newGoal.keyResults.length > 1"
              />
            </div>
            <el-button
              type="primary"
              :icon="Plus"
              @click="addKeyResult"
              style="width: 100%; margin-top: 8px;"
            >
              添加关键结果
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createGoal" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Flag,
  Plus,
  Refresh,
  Clock,
  User,
  OfficeBuilding,
  UserFilled,
  ChatDotRound,
  Delete,
  DataAnalysis,
  Star,
  Grid,
  Share,
  TrendCharts,
  Document
} from '@element-plus/icons-vue'
import axios from 'axios'

// 响应式数据
const activeNav = ref('goals')
const activeTab = ref('goals')
const analysisSubTab = ref('overview')
const searchText = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const ownerFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const selectedProject = ref(1)

// 数据列表
const goalList = ref([])
const userList = ref([])
const projects = ref([
  { id: 1, name: '企业管理系统' },
  { id: 2, name: 'OKR系统' },
  { id: 3, name: '移动应用开发' }
])

// 表单引用
const goalFormRef = ref()

// 新建目标表单
const newGoal = ref({
  title: '',
  description: '',
  type: '',
  owner_id: '',
  dateRange: [],
  weight: 0,
  keyResults: [{ content: '' }]
})

// 表单验证规则
const goalRules = {
  title: [
    { required: true, message: '请输入目标名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择目标类型', trigger: 'change' }
  ],
  owner_id: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
}

// 计算属性
const filteredGoalList = computed(() => {
  let filtered = goalList.value

  // 搜索过滤
  if (searchText.value) {
    filtered = filtered.filter(goal =>
      goal.title.toLowerCase().includes(searchText.value.toLowerCase()) ||
      (goal.owner_name && goal.owner_name.toLowerCase().includes(searchText.value.toLowerCase()))
    )
  }

  // 状态过滤
  if (statusFilter.value !== '') {
    filtered = filtered.filter(goal => goal.status == statusFilter.value)
  }

  // 类型过滤
  if (typeFilter.value) {
    filtered = filtered.filter(goal => goal.type === typeFilter.value)
  }

  // 负责人过滤
  if (ownerFilter.value) {
    filtered = filtered.filter(goal => goal.owner_id == ownerFilter.value)
  }

  // 根据导航筛选
  if (activeNav.value === 'my-goals') {
    // 这里应该根据当前用户ID筛选，暂时用owner_id=1模拟
    filtered = filtered.filter(goal => goal.owner_id === 1)
  } else if (activeNav.value === 'company-goals') {
    filtered = filtered.filter(goal => goal.type === 'company')
  }

  totalCount.value = filtered.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 方法
const fetchGoals = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/goal')
    goalList.value = response.data.map(goal => ({
      ...goal,
      progress: parseFloat(goal.progress) || 0,
      weight: parseFloat(goal.weight) || 0,
      owner_name: goal.owner_name || getUserName(goal.owner_id),
      owner_avatar: getUserAvatar(goal.owner_id)
    }))
  } catch (error) {
    ElMessage.error('获取目标列表失败')
    console.error('获取目标列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/user')
    userList.value = response.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

const getUserName = (userId) => {
  const user = userList.value.find(u => u.id === userId)
  return user ? user.realname : '未知用户'
}

const getUserAvatar = (userId) => {
  // 返回默认头像，实际项目中应该从用户数据中获取
  return 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

const handleTabChange = (tab) => {
  console.log('切换到标签页:', tab)
  // 切换标签页时重置左侧导航
  if (tab === 'goals') {
    activeNav.value = 'goals'
  } else if (tab === 'analysis') {
    activeNav.value = 'analysis-report'
  }
}

const getAnalysisTitle = () => {
  const titles = {
    'analysis-report': '分析报告',
    'operation-report': '运营报告',
    'progress-report': '进度报告',
    'score-report': '评分报告'
  }
  return titles[activeNav.value] || '分析报告'
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const refreshData = () => {
  fetchGoals()
}

// 关键结果相关方法
const addKeyResult = () => {
  newGoal.value.keyResults.push({ content: '' })
}

const removeKeyResult = (index) => {
  newGoal.value.keyResults.splice(index, 1)
}

// 创建目标
const createGoal = async () => {
  if (!goalFormRef.value) return

  try {
    await goalFormRef.value.validate()
    submitting.value = true

    const goalData = {
      title: newGoal.value.title,
      description: newGoal.value.description,
      type: newGoal.value.type,
      owner_id: newGoal.value.owner_id,
      start_date: newGoal.value.dateRange?.[0] || null,
      end_date: newGoal.value.dateRange?.[1] || null,
      weight: newGoal.value.weight || 0,
      progress: 0,
      status: 1,
      department_id: null,
      parent_id: null
    }

    const response = await axios.post('/api/goal', goalData)
    console.log('创建目标响应:', response.data)
    ElMessage.success('目标创建成功')
    showCreateDialog.value = false
    resetForm()
    fetchGoals()
  } catch (error) {
    ElMessage.error('创建目标失败')
    console.error('创建目标失败:', error)
    if (error.response) {
      console.error('错误详情:', error.response.data)
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  newGoal.value = {
    title: '',
    description: '',
    type: '',
    owner_id: '',
    dateRange: [],
    weight: 0,
    keyResults: [{ content: '' }]
  }
  if (goalFormRef.value) {
    goalFormRef.value.resetFields()
  }
}

// 编辑目标
const editGoal = (goal) => {
  ElMessage.info('编辑功能待实现')
}

// 删除目标
const deleteGoal = async (goal) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除目标"${goal.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await axios.delete(`/api/goal/${goal.id}`)
    ElMessage.success('删除成功')
    fetchGoals()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除目标失败:', error)
    }
  }
}

// 生命周期
onMounted(async () => {
  await fetchUsers()
  await fetchGoals()
})
</script>

<style scoped>
.goal-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f6fa;
}

/* 页面头部 */
.page-header {
  background: #fff;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.project-selector {
  min-width: 200px;
}

.header-right {
  display: flex;
  gap: 12px;
}



.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.sidebar-content {
  padding: 16px 0;
}

.nav-section {
  margin-bottom: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
}

.sidebar.collapsed .nav-item {
  padding: 12px;
  justify-content: center;
}

.nav-item:hover {
  background: #f5f7fa;
  color: #409eff;
}

.nav-item.active {
  background: #ecf5ff;
  color: #409eff;
  border-right: 2px solid #409eff;
}



.nav-item .el-icon {
  font-size: 18px;
  min-width: 18px;
}

.nav-item span {
  font-size: 14px;
  white-space: nowrap;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tabs-container {
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.filter-select {
  width: 120px;
}

.content-main {
  flex: 1;
  overflow: auto;
}

.table-container {
  padding: 24px;
}

.analysis-container {
  padding: 24px;
}

.analysis-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.analysis-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.analysis-tabs {
  display: flex;
  gap: 8px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
}

.kr-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kr-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kr-label {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.kr-desc {
  font-size: 14px;
  color: #303133;
}

.goal-info {
  padding: 8px 0;
}

.goal-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.goal-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
}

.owner-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.owner-name {
  font-size: 12px;
  color: #606266;
}

.text-gray {
  color: #c0c4cc;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.key-results {
  width: 100%;
}

.key-result-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.key-result-item:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .toolbar-container {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-right {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .filter-select {
    flex: 1;
    min-width: 100px;
  }
}

/* 表格样式优化 */
.el-table .cell {
  padding: 8px 12px;
}

.el-table__row {
  transition: background-color 0.3s;
}

.el-table__row:hover {
  background-color: #f5f7fa;
}
</style>