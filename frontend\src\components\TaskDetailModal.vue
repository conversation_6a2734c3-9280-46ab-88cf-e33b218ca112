<template>
  <el-dialog 
    v-model="visible" 
    :title="taskData.title" 
    width="80%" 
    :before-close="handleClose"
    class="task-detail-modal"
  >
    <!-- 任务头部信息 -->
    <div class="task-header">
      <div class="task-title-section">
        <div class="task-status-icon" :class="taskData.status">
          <el-icon><component :is="getStatusIcon(taskData.status)" /></el-icon>
        </div>
        <div class="task-info">
          <h3 class="task-title">{{ taskData.title }}</h3>
          <div class="task-meta">
            <span class="task-id">#{{ taskData.id }}</span>
            <span class="task-project">{{ taskData.project }}</span>
          </div>
        </div>
      </div>
      
      <div class="task-actions">
        <el-button :icon="Edit" @click="editTask">编辑</el-button>
        <el-button :icon="Share" @click="shareTask">分享</el-button>
        <el-dropdown @command="handleAction">
          <el-button :icon="MoreFilled" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="copy">复制任务</el-dropdown-item>
              <el-dropdown-item command="move">移动任务</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 任务基本信息 -->
    <div class="task-basic-info">
      <div class="info-row">
        <div class="info-item">
          <span class="label">负责人</span>
          <div class="assignee-info">
            <el-avatar :size="24" :src="taskData.assignee?.avatar" />
            <span class="assignee-name">{{ taskData.assignee?.name || '未分配' }}</span>
          </div>
        </div>
        
        <div class="info-item">
          <span class="label">开始时间</span>
          <div class="date-picker-wrapper">
            <el-date-picker 
              v-model="taskData.startDate" 
              type="date" 
              placeholder="开始时间"
              size="small"
              @change="updateTaskDate"
            />
          </div>
        </div>
        
        <div class="info-item">
          <span class="label">截止时间</span>
          <div class="date-picker-wrapper">
            <el-date-picker 
              v-model="taskData.endDate" 
              type="date" 
              placeholder="截止时间"
              size="small"
              @change="updateTaskDate"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="task-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 任务详情 -->
        <el-tab-pane label="任务详情" name="details">
          <div class="task-details-content">
            <div class="section">
              <h4 class="section-title">任务描述</h4>
              <div class="task-description">
                <el-input 
                  v-if="isEditingDescription"
                  v-model="taskData.description" 
                  type="textarea" 
                  :rows="4"
                  placeholder="请输入任务描述..."
                  @blur="saveDescription"
                />
                <div v-else class="description-display" @click="editDescription">
                  {{ taskData.description || '点击添加任务描述...' }}
                </div>
              </div>
            </div>

            <div class="section">
              <h4 class="section-title">任务属性</h4>
              <div class="task-properties">
                <div class="property-item">
                  <span class="property-label">优先级</span>
                  <el-select v-model="taskData.priority" size="small" @change="updateTaskProperty">
                    <el-option label="高" value="high" />
                    <el-option label="中" value="medium" />
                    <el-option label="低" value="low" />
                  </el-select>
                </div>
                
                <div class="property-item">
                  <span class="property-label">任务类型</span>
                  <el-select v-model="taskData.type" size="small" @change="updateTaskProperty">
                    <el-option label="需求" value="requirement" />
                    <el-option label="缺陷" value="bug" />
                    <el-option label="任务" value="task" />
                    <el-option label="优化" value="improvement" />
                  </el-select>
                </div>
                
                <div class="property-item">
                  <span class="property-label">工作量</span>
                  <el-input-number 
                    v-model="taskData.workload" 
                    :min="0" 
                    :max="100" 
                    size="small"
                    @change="updateTaskProperty"
                  />
                  <span class="unit">小时</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 子任务 -->
        <el-tab-pane name="subtasks">
          <template #label>
            <span>子任务 <el-badge :value="subtasks.length" class="tab-badge" /></span>
          </template>
          <div class="subtasks-content">
            <div class="subtasks-header">
              <el-button type="primary" size="small" :icon="Plus" @click="addSubtask">添加子任务</el-button>
              <div class="subtasks-progress">
                <span>完成进度: {{ subtaskProgress }}%</span>
                <el-progress :percentage="subtaskProgress" :show-text="false" />
              </div>
            </div>
            
            <div class="subtasks-list">
              <div 
                v-for="subtask in subtasks" 
                :key="subtask.id"
                class="subtask-item"
              >
                <el-checkbox 
                  v-model="subtask.completed" 
                  @change="updateSubtaskStatus(subtask)"
                />
                <span class="subtask-title" :class="{ completed: subtask.completed }">
                  {{ subtask.title }}
                </span>
                <div class="subtask-meta">
                  <el-tag :type="getPriorityType(subtask.priority)" size="small">
                    {{ getPriorityText(subtask.priority) }}
                  </el-tag>
                  <span class="subtask-date">{{ formatDate(subtask.dueDate) }}</span>
                </div>
                <el-button text :icon="MoreFilled" @click="editSubtask(subtask)" />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 动态 -->
        <el-tab-pane name="activities">
          <template #label>
            <span>动态 <el-badge :value="activities.length" class="tab-badge" /></span>
          </template>
          <div class="activities-content">
            <div class="activity-input">
              <el-input 
                v-model="newComment" 
                type="textarea" 
                :rows="3"
                placeholder="添加评论..."
              />
              <div class="input-actions">
                <el-button type="primary" size="small" @click="addComment">发表评论</el-button>
              </div>
            </div>
            
            <div class="activities-list">
              <div 
                v-for="activity in activities" 
                :key="activity.id"
                class="activity-item"
              >
                <el-avatar :size="32" :src="activity.user.avatar" />
                <div class="activity-content">
                  <div class="activity-header">
                    <span class="user-name">{{ activity.user.name }}</span>
                    <span class="activity-time">{{ formatDateTime(activity.createdAt) }}</span>
                  </div>
                  <div class="activity-body">
                    <div v-if="activity.type === 'comment'" class="comment">
                      {{ activity.content }}
                    </div>
                    <div v-else class="system-activity">
                      <el-tag size="small" :type="getActivityType(activity.type)">
                        {{ getActivityText(activity.type) }}
                      </el-tag>
                      <span>{{ activity.content }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 附件 -->
        <el-tab-pane name="attachments">
          <template #label>
            <span>附件 <el-badge :value="attachments.length" class="tab-badge" /></span>
          </template>
          <div class="attachments-content">
            <div class="upload-area">
              <el-upload
                class="upload-demo"
                drag
                :action="uploadUrl"
                multiple
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
              </el-upload>
            </div>
            
            <div class="attachments-list">
              <div 
                v-for="attachment in attachments" 
                :key="attachment.id"
                class="attachment-item"
              >
                <el-icon class="file-icon"><Document /></el-icon>
                <div class="attachment-info">
                  <span class="file-name">{{ attachment.name }}</span>
                  <span class="file-size">{{ formatFileSize(attachment.size) }}</span>
                </div>
                <div class="attachment-actions">
                  <el-button text :icon="Download" @click="downloadFile(attachment)">下载</el-button>
                  <el-button text :icon="Delete" @click="deleteFile(attachment)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 底部操作 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <span class="update-time">最后更新: {{ formatDateTime(taskData.updatedAt) }}</span>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="saveTask">保存</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch } from 'vue'
import { 
  Edit, 
  Share, 
  MoreFilled, 
  Plus, 
  Document, 
  Download, 
  Delete, 
  UploadFilled,
  CircleCheck,
  Clock,
  Warning
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'task-updated'])

// 响应式数据
const visible = ref(false)
const activeTab = ref('details')
const isEditingDescription = ref(false)
const newComment = ref('')
const uploadUrl = '/api/upload'

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.taskId) {
    loadTaskData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

watch(() => props.taskId, (newTaskId) => {
  if (newTaskId) {
    loadTaskData()
  }
})

// 任务数据
const taskData = ref({
  id: 1,
  title: '【示例】部门例会',
  description: '讨论本月工作进展和下月计划',
  status: 'doing',
  priority: 'medium',
  type: 'task',
  workload: 2,
  project: '部门管理',
  assignee: {
    name: '张三',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
  },
  startDate: '2024-06-07',
  endDate: '2024-06-07',
  createdAt: '2024-06-01 10:00',
  updatedAt: '2024-06-07 15:30'
})

// 子任务数据
const subtasks = ref([
  {
    id: 1,
    title: '准备会议议程',
    completed: true,
    priority: 'high',
    dueDate: '2024-06-06'
  },
  {
    id: 2,
    title: '通知参会人员',
    completed: false,
    priority: 'medium',
    dueDate: '2024-06-07'
  }
])

// 动态数据
const activities = ref([
  {
    id: 1,
    type: 'comment',
    content: '会议室已预订，时间确认为下午2点',
    user: {
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    createdAt: '2024-06-07 13:15'
  },
  {
    id: 2,
    type: 'status_change',
    content: '将任务状态从"待处理"更改为"进行中"',
    user: {
      name: '李四',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    createdAt: '2024-06-07 10:30'
  }
])

// 附件数据
const attachments = ref([
  {
    id: 1,
    name: '会议议程.docx',
    size: 1024000,
    url: '/files/agenda.docx'
  }
])

// 方法
const loadTaskData = async () => {
  if (!props.taskId) return

  try {
    // 这里可以调用API加载任务数据
    // const response = await axios.get(`/api/tasks/${props.taskId}`)
    // taskData.value = response.data

    // 暂时使用模拟数据，根据taskId设置不同的数据
    const mockTasks = {
      1: {
        id: 1,
        title: '完成用户界面设计',
        description: '设计用户登录和注册界面，包括响应式布局',
        status: 'doing',
        priority: 'high',
        type: 'requirement',
        workload: 8,
        project: '企业管理系统',
        assignee: {
          name: '张三',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        },
        startDate: '2024-06-07',
        endDate: '2024-06-11',
        createdAt: '2024-06-01 10:00',
        updatedAt: '2024-06-07 15:30'
      },
      2: {
        id: 2,
        title: '数据库优化',
        description: '优化数据库查询性能，添加必要的索引',
        status: 'todo',
        priority: 'medium',
        type: 'task',
        workload: 4,
        project: 'OKR系统',
        assignee: {
          name: '李四',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        },
        startDate: '2024-06-12',
        endDate: '2024-06-15',
        createdAt: '2024-06-01 10:00',
        updatedAt: '2024-06-07 15:30'
      }
    }

    const task = mockTasks[props.taskId] || mockTasks[1]
    taskData.value = { ...taskData.value, ...task }

  } catch (error) {
    console.error('加载任务数据失败:', error)
  }
}

const handleClose = () => {
  visible.value = false
}

const handleTabChange = (tabName) => {
  console.log('切换标签页:', tabName)
}

const editTask = () => {
  console.log('编辑任务')
}

const shareTask = () => {
  console.log('分享任务')
}

const handleAction = (command) => {
  console.log('执行操作:', command)
}

const updateTaskDate = () => {
  console.log('更新任务日期')
}

const editDescription = () => {
  isEditingDescription.value = true
}

const saveDescription = () => {
  isEditingDescription.value = false
}

const updateTaskProperty = () => {
  console.log('更新任务属性')
}

const addSubtask = () => {
  console.log('添加子任务')
}

const editSubtask = (subtask) => {
  console.log('编辑子任务:', subtask)
}

const updateSubtaskStatus = (subtask) => {
  console.log('更新子任务状态:', subtask)
}

const addComment = () => {
  if (newComment.value.trim()) {
    activities.value.unshift({
      id: Date.now(),
      type: 'comment',
      content: newComment.value,
      user: {
        name: '当前用户',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      createdAt: new Date().toLocaleString()
    })
    newComment.value = ''
  }
}

const beforeUpload = (file) => {
  console.log('上传前检查:', file)
  return true
}

const handleUploadSuccess = (response, file) => {
  console.log('上传成功:', response, file)
}

const downloadFile = (attachment) => {
  console.log('下载文件:', attachment)
}

const deleteFile = (attachment) => {
  console.log('删除文件:', attachment)
}

const saveTask = () => {
  console.log('保存任务')
  emit('task-updated', taskData.value)
}

const getStatusIcon = (status) => {
  const iconMap = {
    'todo': 'Clock',
    'doing': 'Warning',
    'done': 'CircleCheck'
  }
  return iconMap[status] || 'Clock'
}

const getPriorityType = (priority) => {
  const typeMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return typeMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const textMap = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return textMap[priority] || priority
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

const formatDateTime = (datetime) => {
  if (!datetime) return ''
  return new Date(datetime).toLocaleString()
}

const formatFileSize = (size) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}

const getActivityType = (type) => {
  const typeMap = {
    'comment': 'primary',
    'status_change': 'warning',
    'assignment': 'info',
    'priority_change': 'danger'
  }
  return typeMap[type] || 'info'
}

const getActivityText = (type) => {
  const textMap = {
    'comment': '评论',
    'status_change': '状态变更',
    'assignment': '分配',
    'priority_change': '优先级变更'
  }
  return textMap[type] || type
}

// 计算属性
const subtaskProgress = computed(() => {
  if (!subtasks.value.length) return 0
  const completed = subtasks.value.filter(task => task.completed).length
  return Math.round((completed / subtasks.value.length) * 100)
})
</script>
